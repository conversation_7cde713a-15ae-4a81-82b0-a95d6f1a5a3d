<!-- Generated by SpecStory -->

# Image Analysis for Development Process (2025-05-28 14:12:19)

_**User**_

图片里面是一个研发流程，解析图片获取里面的内容，输出一个 md，然后输出一个mermaid 的流程

---

_**Assistant**_



---



好的，我来帮你解析这张开发流程图，并输出Markdown格式的内容以及Mermaid流程图。

**开发流程 (Markdown 格式)**

# 开发流程

## 需求阶段
*   需求评审
    1.  与需求提出方确认细节以及开发人员进行可行性分析
    2.  参与人员: 产品、测试人员、开发人员
    3.  输出: 调整后的需求文档
*   技术文档
    1.  根据评审的需求输出技术方案
    2.  做技术方案的过程是对需求的梳理
    3.  遇到评审没有想到的地方或疑问点及时与产品交流调整解决
*   方案评审
    1.  开发人员制定概要设计并进行工作量评估
    2.  技术方案评审参与人员: 开发人员、测试、产品
    3.  技术负责人进行方案review，需确认后才可以开始编码。

## 开发阶段
*   代码实现
    1.  开feature分支开发人员进行开发。
    2.  项目经理需定期确认开发进度，若有延期可能需执行措施并告知产品经理。
    3.  有条件的话技术负责人对代码进行review
    4.  输出: 开发成果
*   技术自测
    1.  开发人员部署环境进行自测。
    2.  相关模块进行调试
*   前后端联调
    1.  开发人员部署到开发环境进行自测。
    2.  相关模块进行前后端技术联调(此步非必须)

## 测试阶段
*   提交测试前准备工作
    1.  数据库是否有变更,如有变更sql在提测前要在测试库执行过
    2.  配置文件是否有变更,如有提测前把变更准备好
*   提交测试
    1.  rdc文档上该需求状态为已提测
    2.  在需求下方评论@对应测试
    3.  把设计的系统列上,要测试人员启动哪些系统,需要做的事情。
*   业务测试
    1.  测试人员把测试过程中发现的bug提交到禅道。
    2.  开发人员尽可能当天解决bug.
*   需求变更
    1.  需求变更多发生在这阶段。需先对变更进行是否必要的评估,随后再重新进行需求评审等步骤。
    2.  项目经理根据变更的情况重新执行排期计划。

## 上线阶段
*   上线预发前准备
    1.  数据库是否有变更,有变更走dms平台sql审核执行线上库
    2.  配置文件有变更在gitlab上修改后提交master审核合并
    3.  拉出release分支
    4.  发布单填写
*   上线预发
    1.  系统owner发布release分支
    2.  产品验收
    3.  验收通过把状态修改为已完成
*   上线线前
    1.  合并release分支到develop和master
    2.  再次验证线上配置文件是否正确或者遗漏
*   上线
    1.  系统owner发布线上系统
    2.  遵循兑吧发布流程
*   线上查看
    1.  发布完毕后测试,产品,开发线上看一下确保上线后功能正常

---

**开发流程 (Mermaid 格式)**

```mermaid
graph TD
    A[需求评审] --> B(技术文档)
    B --> C(方案评审)
    C --> D[代码实现]
    D --> E(技术自测)
    E --> F(前后端联调)
    F --> G[提交测试前准备工作]
    G --> H(提交测试)
    H --> I(业务测试)
    I --> J[需求变更]
    J --> C
    I --> K(上线预发前准备)
    K --> L(上线预发)
    L --> M(上线线前)
    M --> N(上线)
    N --> O(线上查看)

    %% 阶段分组
    subgraph 需求阶段
        A
        B
        C
    end

    subgraph 开发阶段
        D
        E
        F
    end

    subgraph 测试阶段
        G
        H
        I
        J
    end

    subgraph 上线阶段
        K
        L
        M
        N
        O
    end

    %% 添加箭头标签或调整布局以更精确匹配原图（可选，需根据实际mermaid能力调整）
    %% 例如:
    %% J -- 重新评审 --> C
    %% F -- 非必须 --> G
```

---

_**User**_

很好，现在需要增加售前跟售后的流程，然后一起嵌入到这个流程里面


售前主要是运营部门（包含销售部） 进行客户初期需求沟通，prd 整理，合同签订，然后将确认的需求转到技术部，正式走刚才的需求研发流程
售后主要是客服部门 进行客户使用功能使用的解答，以及客户的终端用户客诉解决



---

_**Assistant**_



---

好的，我们来把售前和售后流程也加入到开发流程图中。

**开发流程 (增加售前与售后阶段 - Markdown 格式)**

# 开发流程

## 售前阶段
*   客户需求沟通
    1.  运营部门（包含销售部）与客户进行初期需求沟通。
*   PRD 整理
    1.  运营部门整理客户需求，输出PRD（产品需求文档）。
*   合同签订
    1.  与客户签订合同。
*   需求移交
    1.  将确认的需求和PRD移交给技术部。

## 需求阶段
*   需求评审
    1.  与需求提出方（现为技术部接收到售前移交的需求）确认细节以及开发人员进行可行性分析
    2.  参与人员: 产品、测试人员、开发人员
    3.  输出: 调整后的需求文档
*   技术文档
    1.  根据评审的需求输出技术方案
    2.  做技术方案的过程是对需求的梳理
    3.  遇到评审没有想到的地方或疑问点及时与产品交流调整解决
*   方案评审
    1.  开发人员制定概要设计并进行工作量评估
    2.  技术方案评审参与人员: 开发人员、测试、产品
    3.  技术负责人进行方案review，需确认后才可以开始编码。

## 开发阶段
*   代码实现
    1.  开feature分支开发人员进行开发。
    2.  项目经理需定期确认开发进度，若有延期可能需执行措施并告知产品经理。
    3.  有条件的话技术负责人对代码进行review
    4.  输出: 开发成果
*   技术自测
    1.  开发人员部署环境进行自测。
    2.  相关模块进行调试
*   前后端联调
    1.  开发人员部署到开发环境进行自测。
    2.  相关模块进行前后端技术联调(此步非必须)

## 测试阶段
*   提交测试前准备工作
    1.  数据库是否有变更,如有变更sql在提测前要在测试库执行过
    2.  配置文件是否有变更,如有提测前把变更准备好
*   提交测试
    1.  rdc文档上该需求状态为已提测
    2.  在需求下方评论@对应测试
    3.  把设计的系统列上,要测试人员启动哪些系统,需要做的事情。
*   业务测试
    1.  测试人员把测试过程中发现的bug提交到禅道。
    2.  开发人员尽可能当天解决bug.
*   需求变更
    1.  需求变更多发生在这阶段。需先对变更进行是否必要的评估,随后再重新进行需求评审等步骤。
    2.  项目经理根据变更的情况重新执行排期计划。

## 上线阶段
*   上线预发前准备
    1.  数据库是否有变更,有变更走dms平台sql审核执行线上库
    2.  配置文件有变更在gitlab上修改后提交master审核合并
    3.  拉出release分支
    4.  发布单填写
*   上线预发
    1.  系统owner发布release分支
    2.  产品验收
    3.  验收通过把状态修改为已完成
*   上线线前
    1.  合并release分支到develop和master
    2.  再次验证线上配置文件是否正确或者遗漏
*   上线
    1.  系统owner发布线上系统
    2.  遵循兑吧发布流程
*   线上查看
    1.  发布完毕后测试,产品,开发线上看一下确保上线后功能正常

## 售后阶段
*   客户问题解答
    1.  客服部门解答客户功能使用疑问。
*   终端用户客诉解决
    1.  客服部门解决客户终端用户的客诉。

---

**开发流程 (增加售前与售后阶段 - Mermaid 格式)**

```mermaid
graph TD
    A_pre[客户需求沟通] --> B_pre(PRD 整理)
    B_pre --> C_pre(合同签订)
    C_pre --> D_pre(需求移交技术部)

    D_pre --> A[需求评审]

    A[需求评审] --> B(技术文档)
    B --> C(方案评审)
    C --> D[代码实现]
    D --> E(技术自测)
    E --> F(前后端联调)
    F --> G[提交测试前准备工作]
    G --> H(提交测试)
    H --> I(业务测试)
    I --> J[需求变更]
    J --> C

    I --> K(上线预发前准备)
    K --> L(上线预发)
    L --> M(上线线前)
    M --> N(上线)
    N --> O(线上查看)

    O --> P_after[客户问题解答]
    O --> Q_after(终端用户客诉解决)


    %% 阶段分组
    subgraph 售前阶段
        A_pre
        B_pre
        C_pre
        D_pre
    end

    subgraph 需求阶段
        A
        B
        C
    end

    subgraph 开发阶段
        D
        E
        F
    end

    subgraph 测试阶段
        G
        H
        I
        J
    end

    subgraph 上线阶段
        K
        L
        M
        N
        O
    end

    subgraph 售后阶段
        P_after
        Q_after
    end

    %% 添加箭头标签或调整布局以更精确匹配原图（可选，需根据实际mermaid能力调整）
    %% 例如:
    %% J -- 重新评审 --> C
    %% F -- 非必须 --> G
    %% D_pre -- 移交 --> A
    %% O -- 支持 --> P_after
    %% O -- 支持 --> Q_after
```

---

_**User**_

这个图的排版在帮我整理下，以部门为分组，一列一列展示其中的流程

---

_**Assistant**_



---

好的，没问题。我将按照部门来重新组织流程，并输出Markdown和Mermaid流程图。

**开发流程 (按部门分组 - Markdown 格式)**

# 开发流程

## 售前阶段 (运营/销售部门)
*   客户需求沟通
    1.  运营部门（包含销售部）与客户进行初期需求沟通。
*   PRD 整理
    1.  运营部门整理客户需求，输出PRD（产品需求文档）。
*   合同签订
    1.  与客户签订合同。
*   需求移交
    1.  将确认的需求和PRD移交给技术部。

## 技术部阶段
*   **需求阶段**
    *   需求评审
        1.  与需求提出方（现为技术部接收到售前移交的需求）确认细节以及开发人员进行可行性分析
        2.  参与人员: 产品、测试人员、开发人员
        3.  输出: 调整后的需求文档
    *   技术文档
        1.  根据评审的需求输出技术方案
        2.  做技术方案的过程是对需求的梳理
        3.  遇到评审没有想到的地方或疑问点及时与产品交流调整解决
    *   方案评审
        1.  开发人员制定概要设计并进行工作量评估
        2.  技术方案评审参与人员: 开发人员、测试、产品
        3.  技术负责人进行方案review，需确认后才可以开始编码。
*   **开发阶段**
    *   代码实现
        1.  开feature分支开发人员进行开发。
        2.  项目经理需定期确认开发进度，若有延期可能需执行措施并告知产品经理。
        3.  有条件的话技术负责人对代码进行review
        4.  输出: 开发成果
    *   技术自测
        1.  开发人员部署环境进行自测。
        2.  相关模块进行调试
    *   前后端联调
        1.  开发人员部署到开发环境进行自测。
        2.  相关模块进行前后端技术联调(此步非必须)
*   **测试阶段**
    *   提交测试前准备工作
        1.  数据库是否有变更,如有变更sql在提测前要在测试库执行过
        2.  配置文件是否有变更,如有提测前把变更准备好
    *   提交测试
        1.  rdc文档上该需求状态为已提测
        2.  在需求下方评论@对应测试
        3.  把设计的系统列上,要测试人员启动哪些系统,需要做的事情。
    *   业务测试
        1.  测试人员把测试过程中发现的bug提交到禅道。
        2.  开发人员尽可能当天解决bug.
    *   需求变更
        1.  需求变更多发生在这阶段。需先对变更进行是否必要的评估,随后再重新进行需求评审等步骤。
        2.  项目经理根据变更的情况重新执行排期计划。
*   **上线阶段**
    *   上线预发前准备
        1.  数据库是否有变更,有变更走dms平台sql审核执行线上库
        2.  配置文件有变更在gitlab上修改后提交master审核合并
        3.  拉出release分支
        4.  发布单填写
    *   上线预发
        1.  系统owner发布release分支
        2.  产品验收
        3.  验收通过把状态修改为已完成
    *   上线线前
        1.  合并release分支到develop和master
        2.  再次验证线上配置文件是否正确或者遗漏
    *   上线
        1.  系统owner发布线上系统
        2.  遵循兑吧发布流程
    *   线上查看
        1.  发布完毕后测试,产品,开发线上看一下确保上线后功能正常

## 售后阶段 (客服部门)
*   客户问题解答
    1.  客服部门解答客户功能使用疑问。
*   终端用户客诉解决
    1.  客服部门解决客户终端用户的客诉。

---

**开发流程 (按部门分组 - Mermaid 格式)**

```mermaid
graph LR
    subgraph 售前阶段 (运营/销售)
        A_pre[客户需求沟通] --> B_pre(PRD 整理)
        B_pre --> C_pre(合同签订)
        C_pre --> D_pre(需求移交技术部)
    end

    subgraph 技术部
        subgraph 需求阶段
            A[需求评审] --> B(技术文档)
            B --> C(方案评审)
        end
        subgraph 开发阶段
            C --> D[代码实现]
            D --> E(技术自测)
            E --> F(前后端联调)
        end
        subgraph 测试阶段
            F --> G[提交测试前准备工作]
            G --> H(提交测试)
            H --> I(业务测试)
            I --> J[需求变更]
            J --> C %% Re-evaluate solution if requirement changes
        end
        subgraph 上线阶段
            I --> K(上线预发前准备)
            K --> L(上线预发)
            L --> M(上线线前)
            M --> N(上线)
            N --> O(线上查看)
        end
    end

    subgraph 售后阶段 (客服部)
        P_after[客户问题解答]
        Q_after(终端用户客诉解决)
        %% Assuming support starts after going online
    end

    %% Connect stages between departments
    D_pre --> A
    O --> P_after
    O --> Q_after
```

---

_**User**_

Mermaid 有问题，展示不出来

---

_**Assistant**_



---

抱歉，Mermaid 图没有正确展示。有时候复杂的图或者特定的渲染环境可能会出现兼容性问题。

我将尝试简化一下 Mermaid 图的结构，去掉嵌套的子图，只保留最外层的部门分组，看看是否能解决渲染问题。

**开发流程 (按部门分组，简化 Mermaid 结构)**

```mermaid
graph LR
    subgraph 售前阶段 (运营/销售)
        A_pre[客户需求沟通] --> B_pre(PRD 整理)
        B_pre --> C_pre(合同签订)
        C_pre --> D_pre(需求移交技术部)
    end

    subgraph 技术部
        A[需求评审] --> B(技术文档)
        B --> C(方案评审)
        C --> D[代码实现]
        D --> E(技术自测)
        E --> F(前后端联调)
        F --> G[提交测试前准备工作]
        G --> H(提交测试)
        H --> I(业务测试)
        I --> J[需求变更]
        J --> C %% 需求变更后重新评审方案
        I --> K(上线预发前准备)
        K --> L(上线预发)
        L --> M(上线线前)
        M --> N(上线)
        N --> O(线上查看)
    end

    subgraph 售后阶段 (客服部)
        P_after[客户问题解答]
        Q_after(终端用户客诉解决)
        %% 假设售后支持从上线后开始
    end

    %% 连接阶段和部门
    D_pre --> A
    O --> P_after
    O --> Q_after
```

我将内部的“需求阶段”、“开发阶段”等子图去掉了，直接将它们平铺在“技术部”这个最大的子图内。这样结构会更扁平一些，有时可以避免渲染问题。

请您再尝试查看一下这个简化的 Mermaid 图是否能正常显示。如果仍然有问题，可能是您查看的环境对 Mermaid 的支持有限，或者您可以尝试在线的 Mermaid 编辑器（例如：https://mermaid.live/）来粘贴代码进行查看。

---

_**User**_

流程文字的描述，生产 md 的文档给我

---

_**Assistant**_



---

好的，这是包含售前、开发（需求、开发、测试、上线）和售后阶段的完整流程描述 Markdown 文档：

# 开发流程

## 售前阶段 (运营/销售部门)
*   **客户需求沟通**
    1.  运营部门（包含销售部）与客户进行初期需求沟通。
*   **PRD 整理**
    1.  运营部门整理客户需求，输出PRD（产品需求文档）。
*   **合同签订**
    1.  与客户签订合同。
*   **需求移交**
    1.  将确认的需求和PRD移交给技术部。

## 技术部阶段

### 需求阶段
*   **需求评审**
    1.  与需求提出方（现为技术部接收到售前移交的需求）确认细节以及开发人员进行可行性分析
    2.  参与人员: 产品、测试人员、开发人员
    3.  输出: 调整后的需求文档
*   **技术文档**
    1.  根据评审的需求输出技术方案
    2.  做技术方案的过程是对需求的梳理
    3.  遇到评审没有想到的地方或疑问点及时与产品交流调整解决
*   **方案评审**
    1.  开发人员制定概要设计并进行工作量评估
    2.  技术方案评审参与人员: 开发人员、测试、产品
    3.  技术负责人进行方案review，需确认后才可以开始编码。

### 开发阶段
*   **代码实现**
    1.  开feature分支开发人员进行开发。
    2.  项目经理需定期确认开发进度，若有延期可能需执行措施并告知产品经理。
    3.  有条件的话技术负责人对代码进行review
    4.  输出: 开发成果
*   **技术自测**
    1.  开发人员部署环境进行自测。
    2.  相关模块进行调试
*   **前后端联调**
    1.  开发人员部署到开发环境进行自测。
    2.  相关模块进行前后端技术联调(此步非必须)

### 测试阶段
*   **提交测试前准备工作**
    1.  数据库是否有变更,如有变更sql在提测前要在测试库执行过
    2.  配置文件是否有变更,如有提测前把变更准备好
*   **提交测试**
    1.  rdc文档上该需求状态为已提测
    2.  在需求下方评论@对应测试
    3.  把设计的系统列上,要测试人员启动哪些系统,需要做的事情。
*   **业务测试**
    1.  测试人员把测试过程中发现的bug提交到禅道。
    2.  开发人员尽可能当天解决bug.
*   **需求变更**
    1.  需求变更多发生在这阶段。需先对变更进行是否必要的评估,随后再重新进行需求评审等步骤。
    2.  项目经理根据变更的情况重新执行排期计划。

### 上线阶段
*   **上线预发前准备**
    1.  数据库是否有变更,有变更走dms平台sql审核执行线上库
    2.  配置文件有变更在gitlab上修改后提交master审核合并
    3.  拉出release分支
    4.  发布单填写
*   **上线预发**
    1.  系统owner发布release分支
    2.  产品验收
    3.  验收通过把状态修改为已完成
*   **上线线前**
    1.  合并release分支到develop和master
    2.  再次验证线上配置文件是否正确或者遗漏
*   **上线**
    1.  系统owner发布线上系统
    2.  遵循兑吧发布流程
*   **线上查看**
    1.  发布完毕后测试,产品,开发线上看一下确保上线后功能正常

## 售后阶段 (客服部门)
*   **客户问题解答**
    1.  客服部门解答客户功能使用疑问。
*   **终端用户客诉解决**
    1.  客服部门解决客户终端用户的客诉。

---

