{
    //这里的 JDK 地址替换成你本地的
    "java.jdt.ls.java.home": "/Users/<USER>/Documents/openjdk-8u442-b06/jdk1.8.0_442.jdk/Contents/Home",
    "java.configuration.runtimes": [
        {
            "name": "JavaSE-1.8",
            "path": "/Users/<USER>/Documents/openjdk-8u442-b06/jdk1.8.0_442.jdk/Contents/Home",
            "default": true
        }
    ],
    "java.jdt.ls.vmargs": "-Xmx1024m -Xms1024m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m -Xss256k -XX:+UseG1GC",
    "java.import.gradle.home": "/Users/<USER>/Documents/gradle-6.7.1",
    "java.import.gradle.user.home": "/Users/<USER>/Documents/gradle-6.7.1",
    "git.autofetch": true,
    "java.import.gradle.version": "",
    "java.import.gradle.java.home": "/Users/<USER>/Documents/openjdk-8u442-b06/jdk1.8.0_442.jdk/Contents/Home",
    "java.compile.nullAnalysis.mode": "automatic",
    "java.debug.settings.onBuildFailureProceed": true,
    "java.configuration.updateBuildConfiguration": "automatic"
  } 