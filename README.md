# 兑吧开放平台SDK

兑吧开放平台SDK是一个基于Spring Boot的开发工具包，用于简化开发者接入兑吧开放平台的过程。该SDK提供了完整的接口调用能力、安全机制以及开发组件。

## 项目结构

项目采用多模块设计，包含以下模块：

```
duiba-open-sdk/
├── duiba-open-sdk-core/    # SDK核心模块
├── duiba-open-api/         # API服务模块
└── duiba-open-api-client/  # API客户端模块
```

### 模块说明

1. **duiba-open-sdk-core**
   - SDK核心功能实现
   - HTTP客户端封装
   - 签名验证机制
   - 统一的请求/响应模型

2. **duiba-open-api**
   - REST API接口实现
   - 多租户支持
   - 数据持久化
   - 缓存支持

3. **duiba-open-api-client**
   - API调用客户端
   - 自动配置支持
   - 异常处理机制

## 快速开始

### 1. 环境要求

- JDK 1.8+
- Spring Boot 2.7.x
- Maven 3.6+ 或 Gradle 7.x+

### 2. 添加依赖

```groovy
// 使用SDK核心功能
implementation 'com.duiba:duiba-open-sdk-core:1.0.0-SNAPSHOT'

// 使用API客户端
implementation 'com.duiba:duiba-open-api-client:1.0.0-SNAPSHOT'
```

### 3. 基础配置

在`application.yml`中添加配置：

```yaml
duiba:
  open:
    sdk:
      appKey: your-app-key
      appSecret: your-app-secret
      baseUrl: https://api.duiba.com
      connectTimeout: 5000
      readTimeout: 5000
```

### 4. 示例代码

```java
@Service
public class ExampleService {
    
    @Autowired
    private DuibaOpenClient client;
    
    public void example() {
        // 创建请求
        DuibaApiRequest request = DuibaApiRequest.builder()
            .method("example.api.method")
            .params(Collections.singletonMap("key", "value"))
            .build();
            
        // 发送请求
        DuibaApiResponse<ExampleDTO> response = client.execute(request, ExampleDTO.class);
        
        // 处理响应
        if (response.isSuccess()) {
            ExampleDTO data = response.getData();
            // 处理业务逻辑
        }
    }
}
```

## 架构设计

### 1. 核心组件

```mermaid
graph TD
    A[应用层] --> B[SDK Core]
    B --> C[HTTP客户端]
    B --> D[签名机制]
    B --> E[请求/响应模型]
    C --> F[RestTemplate]
    D --> G[MD5签名]
    D --> H[HMAC-SHA256]
    D --> I[RSA签名]
```

### 2. 请求处理流程

```mermaid
sequenceDiagram
    participant App as 应用
    participant Client as SDK客户端
    participant Security as 安全组件
    participant API as 开放平台
    
    App->>Client: 发起请求
    Client->>Security: 生成签名
    Client->>API: 发送请求
    API->>API: 验证签名
    API->>Client: 返回响应
    Client->>App: 处理响应
```

## 最佳实践

1. **错误处理**
   - 使用统一的异常处理机制
   - 合理设置重试策略
   - 记录详细的错误日志

2. **性能优化**
   - 合理配置连接池
   - 使用异步请求
   - 启用响应缓存

3. **安全建议**
   - 妥善保管密钥信息
   - 定期更新密钥
   - 使用HTTPS传输

4. **多租户支持**
   - 实现租户隔离
   - 使用租户上下文
   - 数据权限控制

## 开发指南

详细的开发指南请参考各个模块的文档：

- [SDK核心模块文档](duiba-open-sdk-core/README.md)
- [API服务模块文档](duiba-open-api/README.md)
- [API客户端模块文档](duiba-open-api-client/README.md)

### API文档

API文档使用YApi进行管理和维护：

1. **文档地址**
   - 测试环境：http://yapi.test.duiba.com
   - 生产环境：http://yapi.duiba.com

2. **文档更新流程**
   - 在YApi中创建/更新接口文档
   - 编写接口说明、请求参数、响应示例
   - 添加接口测试用例
   - 进行接口联调测试

3. **接口规范**
   - 统一使用RESTful风格
   - 请求/响应格式统一使用JSON
   - 接口版本通过URL路径区分
   - 遵循统一的响应格式

## 常见问题

### 1. 签名验证失败

- 检查密钥是否正确
- 确认参数排序规则
- 验证签名算法实现

### 2. 请求超时

- 检查网络连接
- 调整超时配置
- 查看服务器负载

### 3. 响应解析错误

- 确认响应格式
- 检查数据模型定义
- 查看错误日志

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交变更
4. 发起 Pull Request

## 版本历史

### 1.0.0-SNAPSHOT
- 初始版本
- 基础功能实现
- 核心组件开发

## 联系我们

- 官方网站：[https://www.duiba.com](https://www.duiba.com)
- 技术支持：<EMAIL>
- 开发者社区：[https://developer.duiba.com](https://developer.duiba.com)