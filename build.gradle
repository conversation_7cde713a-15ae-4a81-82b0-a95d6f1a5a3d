buildscript {
    ext {
        // This is a general fallback, duiba-open-api will use its own.
        springBootVersion = '2.7.18' 
    }
    repositories {
        mavenCentral()
        // Add your company's Nexus if not already here for plugins
        maven { 
            url "http://nexus.dui88.com:8081/nexus/content/groups/public/" 
            allowInsecureProtocol = true 
        }
    }
    dependencies {
        // For propdeps-plugin if used by any module, from your example
        classpath 'org.springframework.build.gradle:propdeps-plugin:0.0.7' 
    }
}

plugins {
    // Declare plugin versions here but apply them selectively in modules
    id 'org.springframework.boot' version '2.2.7.RELEASE' apply false // Align with your company's version for duiba-open-api
    id 'io.spring.dependency-management' version '1.0.15.RELEASE' apply false // A common version, can be overridden
}

allprojects {
    group = 'com.duiba'
    version = '1.0.0-SNAPSHOT' // This can be the overall project version

    repositories {
        mavenLocal()
        maven { 
            url "http://nexus.dui88.com:8081/nexus/content/groups/public/" 
            allowInsecureProtocol = true
        }
        mavenCentral()
    }

    // Common configurations for all projects
    apply plugin: 'java'
    apply plugin: 'idea'
    apply plugin: 'eclipse'
    apply plugin: 'maven-publish' // If all modules need to be publishable

    sourceCompatibility = 1.8
    targetCompatibility = 1.8

    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }
    
    // Common dependency exclusion from your example
    configurations {
        all*.exclude group: 'log4j', module: 'log4j'
        all*.exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    }
}

// Publishing configuration, if needed for all modules similarly
// If it varies greatly, move to individual module build.gradle files
// For now, keeping it here as a general setup
publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
}

// No subprojects.dependencies block here for Spring Boot specific things.
// Lombok can stay if all modules use it, or move to individual ones.
subprojects {
    dependencies {
        compileOnly 'org.projectlombok:lombok:1.18.24' // Assuming 1.18.24 is compatible, or use version from your BOM
        annotationProcessor 'org.projectlombok:lombok:1.18.24'

        testImplementation 'junit:junit:4.13.2' // Or a version compatible with Spring Boot 2.2.7
    }

    test {
        useJUnitPlatform() // JUnit 5 platform, ensure compatibility if using older JUnit 4 tests primarily
        ignoreFailures = true // From your example
    }
} 