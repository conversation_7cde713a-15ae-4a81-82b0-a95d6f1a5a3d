# duiba-open-api-biz

兑吧开放平台API业务服务模块，提供REST API接口实现和核心业务逻辑。本模块采用DDD（领域驱动设计）思想，实现了多租户隔离、接口访问控制、数据持久化等功能。

## 技术栈

- Spring Boot 2.7.x
- MyBatis
- Redis
- MySQL
- Spring Security

## 模块结构

```
src/main/java/com/duiba/open/api/
├── application/        # 应用层
│   ├── dto/           # 数据传输对象
│   ├── assembler/     # 对象转换器
│   └── service/       # 应用服务
├── domain/            # 领域层
│   ├── model/         # 领域模型
│   ├── service/       # 领域服务
│   └── repository/    # 仓储接口
├── infrastructure/    # 基础设施层
│   ├── config/        # 配置类
│   ├── persistence/   # 持久化实现
│   └── integration/   # 外部集成
└── interfaces/        # 接口层
    ├── controller/    # 控制器
    ├── facade/        # 外观接口
    └── assembler/     # 接口层对象转换
```

## 领域模型

### 1. 租户（Tenant）
```java
@Data
@Builder
public class Tenant {
    private Long id;
    private String code;
    private String name;
    private TenantStatus status;
    private Map<String, String> config;
    
    public boolean isActive() {
        return TenantStatus.ACTIVE.equals(status);
    }
}
```

### 2. 应用（Application）
```java
@Data
@Builder
public class Application {
    private Long id;
    private String appKey;
    private String appSecret;
    private Long tenantId;
    private ApplicationStatus status;
    private Set<String> permissions;
    
    public boolean hasPermission(String permission) {
        return permissions.contains(permission);
    }
}
```

## 接口设计

### 1. RESTful API

```java
@RestController
@RequestMapping("/api/v1")
public class ApplicationController {
    
    @PostMapping("/applications")
    public Result<ApplicationDTO> createApplication(
            @RequestBody @Valid CreateApplicationRequest request) {
        // 实现创建应用逻辑
    }
    
    @GetMapping("/applications/{id}")
    public Result<ApplicationDTO> getApplication(@PathVariable Long id) {
        // 实现获取应用详情逻辑
    }
}
```

### 2. 统一响应格式

```java
@Data
@Builder
public class Result<T> {
    private boolean success;
    private String code;
    private String message;
    private T data;
    
    public static <T> Result<T> success(T data) {
        return Result.<T>builder()
            .success(true)
            .code("200")
            .data(data)
            .build();
    }
    
    public static <T> Result<T> error(String code, String message) {
        return Result.<T>builder()
            .success(false)
            .code(code)
            .message(message)
            .build();
    }
}
```

## 多租户实现

### 1. 租户上下文

```java
@Component
public class TenantContext {
    private static final ThreadLocal<String> currentTenant = new ThreadLocal<>();
    
    public static void setTenant(String tenantId) {
        currentTenant.set(tenantId);
    }
    
    public static String getTenant() {
        return currentTenant.get();
    }
    
    public static void clear() {
        currentTenant.remove();
    }
}
```

### 2. 租户拦截器

```java
@Component
public class TenantInterceptor implements HandlerInterceptor {
    
    @Override
    public boolean preHandle(HttpServletRequest request, 
            HttpServletResponse response, Object handler) {
        String tenantId = request.getHeader("X-Tenant-ID");
        if (StringUtils.isNotBlank(tenantId)) {
            TenantContext.setTenant(tenantId);
        }
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, 
            HttpServletResponse response, Object handler, Exception ex) {
        TenantContext.clear();
    }
}
```

### 3. 数据隔离

```java
@Component
@Aspect
public class TenantIsolationAspect {
    
    @Around("@annotation(com.duiba.open.api.annotation.TenantIsolation)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        String tenantId = TenantContext.getTenant();
        if (StringUtils.isBlank(tenantId)) {
            throw new TenantNotFoundException("租户ID不能为空");
        }
        return point.proceed();
    }
}
```

## 数据访问

### 1. MyBatis配置

```yaml
mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
  type-aliases-package: com.duiba.open.api.domain.model
```

### 2. 实体映射

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.duiba.open.api.infrastructure.persistence.ApplicationMapper">
    <select id="findByAppKey" resultType="Application">
        SELECT * FROM t_application 
        WHERE app_key = #{appKey} 
        AND tenant_id = #{tenantId}
    </select>
</mapper>
```

### 3. 缓存配置

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
```

## 安全机制

### 1. 接口认证

```java
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeRequests()
            .antMatchers("/api/v1/public/**").permitAll()
            .anyRequest().authenticated()
            .and()
            .addFilter(new JwtAuthenticationFilter(authenticationManager()));
    }
}
```

### 2. 访问控制

```java
@PreAuthorize("hasPermission(#request.tenantId, 'APPLICATION_MANAGE')")
public ApplicationDTO createApplication(CreateApplicationRequest request) {
    // 实现创建应用逻辑
}
```

### 3. 操作审计

```java
@Aspect
@Component
public class AuditLogAspect {
    
    @Around("@annotation(com.duiba.open.api.annotation.AuditLog)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        // 记录操作日志
        return point.proceed();
    }
}
```

## API文档

API文档使用YApi进行管理，访问地址：`http://yapi.duiba.com`

### 1. 接口规范

- 统一使用RESTful风格
- 请求/响应格式统一使用JSON
- 接口版本通过URL路径区分，如 `/api/v1/`
- 统一响应格式，包含状态码、消息和数据

### 2. 文档更新

1. 在YApi中创建接口文档
2. 编写接口说明、请求参数、响应示例
3. 添加接口测试用例
4. 导出接口文档（支持HTML、Markdown格式）

### 3. 接口测试

1. 使用YApi的Mock服务进行接口测试
2. 配置测试环境
3. 运行自动化测试

### 4. 权限管理

- 项目权限控制
- 接口访问权限
- 文档编辑权限

## 单元测试

### 1. 控制器测试

```java
@SpringBootTest
@AutoConfigureMockMvc
class ApplicationControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Test
    void testCreateApplication() throws Exception {
        CreateApplicationRequest request = new CreateApplicationRequest();
        // 设置请求参数
        
        mockMvc.perform(post("/api/v1/applications")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtil.toJson(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true));
    }
}
```

### 2. 服务测试

```java
@SpringBootTest
class ApplicationServiceTest {
    
    @Autowired
    private ApplicationService applicationService;
    
    @MockBean
    private ApplicationRepository applicationRepository;
    
    @Test
    void testCreateApplication() {
        // 准备测试数据
        CreateApplicationRequest request = new CreateApplicationRequest();
        Application application = new Application();
        
        when(applicationRepository.save(any())).thenReturn(application);
        
        // 执行测试
        ApplicationDTO result = applicationService.createApplication(request);
        
        // 验证结果
        assertNotNull(result);
        verify(applicationRepository).save(any());
    }
}
```

## 性能优化

### 1. 缓存策略

```java
@Cacheable(value = "applications", key = "#appKey")
public ApplicationDTO getByAppKey(String appKey) {
    Application application = applicationRepository.findByAppKey(appKey);
    return applicationAssembler.toDTO(application);
}
```

### 2. 数据库优化

```sql
-- 创建索引
CREATE INDEX idx_app_key ON t_application(app_key, tenant_id);

-- 分区表
CREATE TABLE t_application (
    id BIGINT PRIMARY KEY,
    app_key VARCHAR(32),
    tenant_id BIGINT
) PARTITION BY HASH(tenant_id) PARTITIONS 4;
```

### 3. 异步处理

```java
@Async
public CompletableFuture<Void> processAsync(String appKey) {
    return CompletableFuture.runAsync(() -> {
        // 异步处理逻辑
    });
}
```

## 监控告警

### 1. 健康检查

```java
@Component
public class RedisHealthIndicator extends AbstractHealthIndicator {
    
    @Override
    protected void doHealthCheck(Builder builder) throws Exception {
        // 实现Redis健康检查
    }
}
```

### 2. 指标收集

```java
@Component
public class ApiMetrics {
    
    private final MeterRegistry registry;
    
    public void recordApiCall(String api, long duration) {
        registry.timer("api.calls", "name", api)
            .record(duration, TimeUnit.MILLISECONDS);
    }
}
```

## 部署配置

### 1. 应用配置

```yaml
server:
  port: 8080
  servlet:
    context-path: /open-api
  tomcat:
    max-threads: 200
    min-spare-threads: 20

spring:
  application:
    name: duiba-open-api
  profiles:
    active: dev
```

### 2. 日志配置

```xml
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/duiba-open-api.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/duiba-open-api.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>
    
    <root level="INFO">
        <appender-ref ref="FILE" />
    </root>
</configuration>
```

## 常见问题

### 1. 租户隔离失效
- 检查租户上下文是否正确设置
- 验证数据库查询条件
- 确认缓存key是否包含租户信息

### 2. 接口超时
- 检查数据库索引
- 优化SQL查询
- 调整连接池配置

### 3. 缓存异常
- 验证Redis连接
- 检查缓存配置
- 确认序列化方式

## 版本历史

### 1.0.0-SNAPSHOT
- 初始版本
- 基础功能实现
- 多租户支持
- 接口鉴权
- 数据隔离 