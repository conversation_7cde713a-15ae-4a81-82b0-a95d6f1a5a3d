plugins {
    id 'org.springframework.boot' // Version will be managed by dependencyManagement or buildscript
    id 'io.spring.dependency-management'
    // id 'java' // Applied from root
}

// Let Spring Boot plugin version be driven by what's in dependencyManagement via the BOM
// Or explicitly set it here if needed, but usually BOM handles this.
// If you need to declare the plugin version directly here for this module:
// buildscript {
//     dependencies {
//         classpath "org.springframework.boot:spring-boot-gradle-plugin:2.2.7.RELEASE"
//     }
// }

dependencyManagement {
    imports {
        mavenBom 'cn.com.duiba.boot:spring-boot-ext-dependencies:2.0.0-h87'
        mavenBom "org.springframework.boot:spring-boot-dependencies:2.2.7.RELEASE"
        mavenBom 'org.springframework.cloud:spring-cloud-dependencies:Hoxton.SR6'
    }
}

dependencies {
    implementation project(':duiba-open-sdk-core')
    // api-client might be needed if open-api calls other internal services via Feign defined in api-client
    // For now, assuming it's primarily a server. If it also acts as a client to other duiba services:
    // implementation project(':duiba-open-api-client') 

    // Spring Boot & Cloud Core
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign' // For calling other web layers

    // Your company's custom starters
    implementation "cn.com.duiba.boot:spring-boot-starter-cat"
    implementation "cn.com.duiba.boot:spring-boot-starter-perftest"
    implementation "cn.com.duiba.boot:spring-boot-starter-accesslog"
    implementation "cn.com.duiba.boot:spring-boot-starter-redis" // From your example
    // implementation "cn.com.duiba.boot:spring-boot-starter-rocketmq" // If needed

    // Data persistence (if still directly used, otherwise remove if only via Center layer)
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter' // Version from BOM
    implementation 'mysql:mysql-connector-java' // Version from BOM

    // Other dependencies from your example that might be relevant for this API module
    // compile("org.apache.zookeeper:zookeeper:3.4.8") { exclude group: 'org.slf4j' } // Example
    // compile("com.github.sgroschupf:zkclient:0.1") { exclude group: 'org.slf4j' } // Example
    implementation 'com.google.guava:guava' // Version from BOMs (e.g., 31.1-jre if compatible or overridden)

    // Lombok and test dependencies are typically inherited from root or defined if specific versions needed
    // testImplementation 'org.springframework.boot:spring-boot-starter-test' // Handled by BOM
    testImplementation 'com.h2database:h2'

    // For SDK integration testing with a mock server - MOVED to duiba-open-sdk-core
    // testImplementation 'com.github.tomakehurst:wiremock-jre8:2.35.1'
    // testImplementation 'ch.qos.logback:logback-classic:1.2.11' // For test logging, if not covered
}

// Ensure this module produces an executable Boot JAR
bootJar {
    enabled = true
}

jar {
    enabled = false // Typically for a Spring Boot app, you want the bootJar not the plain jar
} 