package com.duiba.open.api;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableDuibaFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableAutoConfiguration(exclude = {
        // DataSourceTransactionManagerAutoConfiguration.class, // This was in your example, but might conflict if you DO use transactions
        DataSourceAutoConfiguration.class, // Exclude if DB config comes from spring-project.xml or Apollo entirely
        PersistenceExceptionTranslationAutoConfiguration.class // Related to Spring Data JPA, exclude if not used or configured elsewhere
})
@EnableDiscoveryClient // For Eureka registration
@EnableCircuitBreaker // For Hystrix or Resilience4j
@EnableScheduling // If you have @Scheduled tasks
@EnableDuibaFeignClients("cn.com.duiba") // Or more specific base packages for your Web Layer Feign clients
public class DuibaOpenApiApplication {

    /**
     * Main method to start the Spring Boot application.
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(DuibaOpenApiApplication.class, args);
    }
} 