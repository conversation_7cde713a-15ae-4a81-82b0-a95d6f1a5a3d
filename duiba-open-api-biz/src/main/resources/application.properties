#tomcat
server.port=7755
server.tomcat.uri-encoding=UTF-8
server.tomcat.protocol_header=x-forwarded-proto
#spring
spring.jmx.enabled=false

server.tomcat.max-threads=500


server.error.whitelabel.enabled=false

duiba.threadpool.enabled=true
duiba.threadpool.core-size=100


duiba.feign.serialization=hessian2
#\u65B0\u63A5\u53E3\u7EDF\u4E00\u524D\u7F00


spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=10MB
