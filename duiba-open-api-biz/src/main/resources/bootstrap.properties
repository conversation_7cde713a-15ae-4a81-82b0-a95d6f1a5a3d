# Spring Cloud Config Server
spring.cloud.config.uri=https://configserver.duibatest.com.cn
spring.cloud.config.fail-fast=true
# spring.cloud.config.name= # Optional: if different from application name
# spring.cloud.config.profile=${spring.profiles.active} # Optional: if different from application active profiles
# spring.cloud.config.label= # Optional: if using a specific git branch for config

# Application Name for Eureka and Config Server
spring.application.name=duiba-open-api 
# Active profiles (e.g., test, dev, prod) - should match what Config Server expects
spring.profiles.active=test 

# Spring Boot DevTools (optional, from your example)
spring.devtools.restart.enabled=true

# Eureka Client Configuration (Basic - more can be added in application.yml/properties from Config Server)
# eureka.client.serviceUrl.defaultZone=http://eureka-server1:port/eureka/,http://eureka-server2:port/eureka/
# eureka.instance.prefer-ip-address=true 