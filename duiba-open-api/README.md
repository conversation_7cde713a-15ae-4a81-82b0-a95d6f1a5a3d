# duiba-open-api-client

兑吧开放平台API客户端模块，提供便捷的API调用封装。本模块基于SDK核心模块构建，提供了更高层次的抽象和便捷的调用方式。

## 技术栈

- Spring Boot 2.7.x
- duiba-open-sdk-core
- Spring Retry
- Spring Cache
- Lombok

## 模块结构

```
src/main/java/com/duiba/open/api/client/
├── config/             # 配置类
├── interceptor/        # 拦截器
├── properties/         # 配置属性
├── model/             # 数据模型
└── service/           # 服务实现
```

## 核心功能

### 1. 自动配置
- Spring Boot自动配置支持
- 可配置的客户端参数
- 灵活的扩展点

### 2. 请求处理
- 请求重试机制
- 响应缓存支持
- 异常处理机制

### 3. 接口封装
- 业务接口封装
- 类型安全的API
- 参数校验

## 配置说明

### 1. Maven/Gradle依赖

```groovy
dependencies {
    implementation 'com.duiba:duiba-open-api-client:1.0.0-SNAPSHOT'
}
```

### 2. 应用配置

```yaml
duiba:
  open:
    api:
      client:
        # 应用标识
        appKey: your-app-key
        # 应用密钥
        appSecret: your-app-secret
        # API基础地址
        baseUrl: https://api.duiba.com
        # HTTP客户端配置
        http:
          connectTimeout: 5000
          readTimeout: 5000
          maxConnTotal: 200
          maxConnPerRoute: 50
        # 重试配置
        retry:
          maxAttempts: 3
          backoffPeriod: 1000
        # 缓存配置
        cache:
          enabled: true
          ttl: 300
```

## 核心类说明

### 1. API客户端配置

```java
@Configuration
@EnableConfigurationProperties(DuibaApiClientProperties.class)
public class DuibaApiClientAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public DuibaApiClient duibaApiClient(DuibaApiClientProperties properties) {
        return DuibaApiClient.builder()
            .properties(properties)
            .build();
    }
    
    @Bean
    @ConditionalOnProperty(prefix = "duiba.open.api.client.retry", 
        name = "enabled", havingValue = "true")
    public RetryTemplate retryTemplate(DuibaApiClientProperties properties) {
        RetryTemplate template = new RetryTemplate();
        
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(properties.getRetry().getMaxAttempts());
        
        FixedBackOffPolicy backOffPolicy = new FixedBackOffPolicy();
        backOffPolicy.setBackOffPeriod(properties.getRetry().getBackoffPeriod());
        
        template.setRetryPolicy(retryPolicy);
        template.setBackOffPolicy(backOffPolicy);
        
        return template;
    }
}
```

### 2. API客户端实现

```java
@Slf4j
public class DuibaApiClient {
    
    private final DuibaOpenClient openClient;
    private final RetryTemplate retryTemplate;
    private final CacheManager cacheManager;
    
    public <T> T execute(String method, Object request, Class<T> responseType) {
        return retryTemplate.execute(context -> {
            String cacheKey = generateCacheKey(method, request);
            T cachedResult = getFromCache(cacheKey, responseType);
            if (cachedResult != null) {
                return cachedResult;
            }
            
            DuibaApiRequest apiRequest = buildRequest(method, request);
            DuibaApiResponse<T> response = openClient.execute(apiRequest, responseType);
            
            if (response.isSuccess()) {
                putToCache(cacheKey, response.getData());
                return response.getData();
            }
            
            throw new DuibaApiException(response.getCode(), response.getMessage());
        });
    }
    
    private <T> T getFromCache(String key, Class<T> type) {
        if (!properties.getCache().isEnabled()) {
            return null;
        }
        return cacheManager.getCache("duiba_api").get(key, type);
    }
    
    private void putToCache(String key, Object value) {
        if (!properties.getCache().isEnabled()) {
            return;
        }
        cacheManager.getCache("duiba_api").put(key, value);
    }
}
```

### 3. 拦截器实现

```java
@Component
public class ApiClientInterceptor implements ClientHttpRequestInterceptor {
    
    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        
        // 1. 记录请求开始时间
        long startTime = System.currentTimeMillis();
        
        try {
            // 2. 添加通用请求头
            request.getHeaders().add("X-Client-Version", "1.0.0");
            request.getHeaders().add("X-Request-Id", UUID.randomUUID().toString());
            
            // 3. 执行请求
            ClientHttpResponse response = execution.execute(request, body);
            
            // 4. 记录响应时间
            long duration = System.currentTimeMillis() - startTime;
            log.info("API请求耗时: {}ms, URL: {}", duration, request.getURI());
            
            return response;
        } catch (Exception e) {
            log.error("API请求异常", e);
            throw e;
        }
    }
}
```

## 使用示例

### 1. 基础调用

```java
@Service
@Slf4j
public class ExampleService {
    
    @Autowired
    private DuibaApiClient apiClient;
    
    public void example() {
        try {
            // 创建请求对象
            ExampleRequest request = ExampleRequest.builder()
                .param1("value1")
                .param2("value2")
                .build();
            
            // 执行请求
            ExampleResponse response = apiClient.execute(
                "example.api.method", 
                request, 
                ExampleResponse.class
            );
            
            // 处理响应
            log.info("请求成功: {}", response);
            
        } catch (DuibaApiException e) {
            log.error("API调用失败: code={}, message={}", 
                e.getErrorCode(), e.getErrorMsg());
        }
    }
}
```

### 2. 批量调用

```java
@Service
public class BatchService {
    
    @Autowired
    private DuibaApiClient apiClient;
    
    public List<Response> batchCall(List<Request> requests) {
        return requests.parallelStream()
            .map(request -> {
                try {
                    return apiClient.execute("batch.method", request, Response.class);
                } catch (Exception e) {
                    log.error("批量调用失败", e);
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
}
```

### 3. 异步调用

```java
@Service
public class AsyncService {
    
    @Autowired
    private DuibaApiClient apiClient;
    
    @Async
    public CompletableFuture<Response> asyncCall(Request request) {
        return CompletableFuture.supplyAsync(() -> 
            apiClient.execute("async.method", request, Response.class)
        );
    }
}
```

## 异常处理

### 1. 全局异常处理

```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(DuibaApiException.class)
    public ResponseEntity<ErrorResponse> handleApiException(DuibaApiException e) {
        ErrorResponse error = ErrorResponse.builder()
            .code(e.getErrorCode())
            .message(e.getErrorMsg())
            .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
            .body(error);
    }
}
```

### 2. 重试处理

```java
@Configuration
public class RetryConfig {
    
    @Bean
    public RetryTemplate retryTemplate() {
        RetryTemplate template = new RetryTemplate();
        
        // 配置重试策略
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(3);
        
        // 配置退避策略
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(1000);
        backOffPolicy.setMultiplier(2);
        backOffPolicy.setMaxInterval(10000);
        
        template.setRetryPolicy(retryPolicy);
        template.setBackOffPolicy(backOffPolicy);
        
        return template;
    }
}
```

## 性能优化

### 1. 连接池配置

```yaml
duiba:
  open:
    api:
      client:
        pool:
          maxTotal: 200
          maxPerRoute: 50
          validateAfterInactivity: 2000
          keepAliveTimeout: 5000
```

### 2. 缓存配置

```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .maximumSize(10000));
            
        return cacheManager;
    }
}
```

## 监控指标

### 1. 性能指标

```java
@Component
public class ApiMetrics {
    
    private final MeterRegistry registry;
    
    public void recordApiCall(String method, long duration) {
        registry.timer("api.client.calls")
            .tags("method", method)
            .record(duration, TimeUnit.MILLISECONDS);
    }
    
    public void recordApiError(String method, String errorCode) {
        registry.counter("api.client.errors")
            .tags("method", method, "code", errorCode)
            .increment();
    }
}
```

### 2. 健康检查

```java
@Component
public class ApiClientHealthIndicator extends AbstractHealthIndicator {
    
    private final DuibaApiClient apiClient;
    
    @Override
    protected void doHealthCheck(Builder builder) throws Exception {
        try {
            apiClient.execute("health.check", null, Void.class);
            builder.up();
        } catch (Exception e) {
            builder.down()
                .withException(e);
        }
    }
}
```

## 最佳实践

### 1. 错误处理
- 使用统一的异常处理机制
- 实现优雅的降级策略
- 添加详细的错误日志

### 2. 性能优化
- 合理配置连接池
- 启用响应缓存
- 使用异步调用

### 3. 监控告警
- 实现健康检查
- 收集性能指标
- 设置告警阈值

## 常见问题

### 1. 连接超时
- 检查网络连接
- 调整超时配置
- 验证目标服务

### 2. 请求失败
- 检查请求参数
- 验证签名配置
- 查看错误日志

### 3. 性能问题
- 优化连接池
- 启用缓存
- 使用异步调用

## 版本历史

### 1.0.0-SNAPSHOT
- 初始版本
- 基础功能实现
- 自动配置支持
- 重试机制
- 缓存支持 