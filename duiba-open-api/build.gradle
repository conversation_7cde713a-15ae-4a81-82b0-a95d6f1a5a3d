plugins {
    // id 'org.springframework.boot' // Do not apply Spring Boot plugin to make it executable
    id 'io.spring.dependency-management'
    id 'java-library' // Use java-library for better API/implementation separation
}

// Let Spring Boot plugin version be driven by what's in dependencyManagement via the BOM
// Or explicitly set it here if needed, but usually BOM handles this.
// If you need to declare the plugin version directly here for this module:
// buildscript {
//     dependencies {
//         classpath "org.springframework.boot:spring-boot-gradle-plugin:2.2.7.RELEASE"
//     }
// }

dependencyManagement {
    imports {
        // BOMs for consistent versions, especially for transitive dependencies of Feign
        mavenBom 'cn.com.duiba.boot:spring-boot-ext-dependencies:2.0.0-h87'
        mavenBom "org.springframework.boot:spring-boot-dependencies:2.2.7.RELEASE"
        mavenBom 'org.springframework.cloud:spring-cloud-dependencies:Hoxton.SR6'
    }
}

dependencies {
    // This module provides Feign clients, so it needs OpenFeign
    api 'org.springframework.cloud:spring-cloud-starter-openfeign'

    // It will likely use DTOs/models defined in or shared with sdk-core
    api project(':duiba-open-sdk-core')

    // For DTO validation annotations if used in Feign interfaces/DTOs
    api 'javax.validation:validation-api' // Or jakarta.validation:jakarta.validation-api if moving to newer standards

    // Jackson for DTO serialization, typically brought in by openfeign or web starters,
    // but explicitly adding if DTOs are complex or need specific Jackson features.
    // api 'com.fasterxml.jackson.core:jackson-databind' // Usually transitive from openfeign

    implementation 'javax.servlet:servlet-api:2.5'
    testImplementation 'org.junit.jupiter:junit-jupiter:5.9.0' // 确保使用最新的JUnit 5版本
    implementation 'com.alibaba:fastjson:1.2.5'

    // 添加日志框架
    implementation 'org.slf4j:slf4j-api:1.7.30'
    implementation 'ch.qos.logback:logback-classic:1.2.3'
}

// This module is a library, not a runnable Spring Boot application
/*
bootJar {
    enabled = false
}
*/

jar {
    enabled = true
} 