package com.duiba.open.api.client;

import com.duiba.open.api.client.credits.creditRequest.BaseCreditUrlTool;
import com.duiba.open.api.client.credits.param.CreditAuditParams;
import com.duiba.open.api.client.credits.param.CreditConfirmParams;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertTrue;

public class DuibaSdkIntegrationTest {

    private BaseCreditUrlTool creditTool;

    @BeforeEach
    public void setUp() {
        // 线上测试
        // 初始化 CreditTool，传入 appKey、appSecret 和 activityBaseUrl
        creditTool = new BaseCreditUrlTool("xztLHKX3f15183yL5M9rwie5wAW", "37323HBbXj1dZsHGp2dbvJ8RuLGd", "https://52083-activity.dbsa14.cn");
    }

    @Test
    public void testBuildAutoLoginRequest() {
        String result = creditTool.buildAutoLoginRequest("testUid", 100L, "");
        // 这里可以根据预期的 URL 进行断言
        System.out.println("免登url = " + result);
        assertTrue(result.contains("uid=testUid"));
        assertTrue(result.contains("credits=100"));
    }

    @Test
    public void testBuildCreditOrderStatusRequest() {
        String result = creditTool.buildCreditOrderStatusRequest("testOrderNum", "testBizId");
        // 这里可以根据预期的 URL 进行断言
        assertTrue(result.contains("orderNum=testOrderNum"));
        assertTrue(result.contains("bizId=testBizId"));
    }

    @Test
    public void testBuildCreditAuditRequest() {
        CreditAuditParams params = new CreditAuditParams();
        params.setPassOrderNums(Arrays.asList("order1", "order2"));
        params.setRejectOrderNums(Arrays.asList("order3"));

        String result = creditTool.buildCreditAuditRequest(params);
        // 这里可以根据预期的 URL 进行断言
        assertTrue(result.contains("passOrderNums=order1,order2"));
        assertTrue(result.contains("rejectOrderNums=order3"));
    }

    @Test
    public void testBuildCreditConfirmRequest() {
        CreditConfirmParams params = new CreditConfirmParams();
        params.setSuccess(true);
        params.setErrorMessage("");
        params.setOrderNum("testOrderNum");

        String result = creditTool.buildCreditConfirmRequest(params);
        // 这里可以根据预期的 URL 进行断言
        assertTrue(result.contains("success=true"));
        assertTrue(result.contains("orderNum=testOrderNum"));
    }

    @Test
    public void testQueryForFrontItem() {
        String result = creditTool.queryForFrontItem("5");
        // 这里可以根据预期的 URL 进行断言
        assertTrue(result.contains("count=5"));
    }

    @Test
    public void testGetActivityTimes() {
        String result = creditTool.getActivityTimes("testUid", "testActivityId", "testValidType", "10", "testBizId");
        // 这里可以根据预期的 URL 进行断言
        assertTrue(result.contains("uid=testUid"));
        assertTrue(result.contains("activityId=testActivityId"));
        assertTrue(result.contains("times=10"));
        assertTrue(result.contains("bizId=testBizId"));
    }
} 