# duiba-open-sdk-core

兑吧开放平台SDK核心模块，提供基础的SDK功能实现。本模块是整个SDK的核心，提供了HTTP客户端封装、签名验证机制、统一的请求/响应模型等基础功能。

## 技术栈

- Spring Boot 2.7.x
- RestTemplate
- FastJSON
- Commons Lang3
- Commons Codec

## 核心功能

### 1. HTTP客户端
- 基于RestTemplate的HTTP客户端封装
- 支持自定义请求拦截器
- 内置重试机制
- 超时控制
- 连接池管理

### 2. 签名机制
- 支持多种签名算法：
  - MD5（默认）
  - HMAC-SHA256
  - RSA
- 签名参数标准化处理
- 签名验证工具
- 可扩展的签名器接口

### 3. 请求/响应模型
- 统一的请求对象封装
- 标准化的响应结果
- 参数验证
- 错误码体系

### 4. 安全特性
- 请求防重放
- 时间戳验证
- 参数加密
- 签名验证

## 目录结构

```
src/main/java/com/duiba/sdk/
├── client/             # 客户端实现
├── config/             # 配置类
├── constant/           # 常量定义
├── exception/          # 异常类
├── model/              # 数据模型
├── security/           # 安全相关
└── util/              # 工具类
```

## 核心类说明

### 1. DuibaOpenClient

主要的SDK客户端类，提供接口调用能力：

```java
@Slf4j
@Builder
public class DuibaOpenClient {
    private final String appKey;
    private final String appSecret;
    private final String baseUrl;
    private final RestTemplate restTemplate;
    private final SignatureService signatureService;
    
    public <T> DuibaApiResponse<T> execute(DuibaApiRequest request, Class<T> responseType) {
        // 实现请求执行逻辑
    }
}
```

### 2. SignatureService

签名服务接口，定义签名生成和验证方法：

```java
public interface SignatureService {
    String sign(Map<String, Object> params, String secret);
    boolean verify(Map<String, Object> params, String secret, String sign);
}
```

## 配置说明

### 1. application.yml配置

```yaml
duiba:
  open:
    sdk:
      # 应用标识
      appKey: your-app-key
      # 应用密钥
      appSecret: your-app-secret
      # API基础地址
      baseUrl: https://api.duiba.com
      # HTTP客户端配置
      http:
        connectTimeout: 5000
        readTimeout: 5000
        maxConnTotal: 200
        maxConnPerRoute: 50
      # 重试配置
      retry:
        maxAttempts: 3
        backoffPeriod: 1000
```

### 2. Java配置类

```java
@Configuration
@EnableConfigurationProperties(DuibaOpenProperties.class)
public class DuibaOpenAutoConfiguration {
    
    @Bean
    public DuibaOpenClient duibaOpenClient(DuibaOpenProperties properties) {
        return DuibaOpenClient.builder()
            .appKey(properties.getAppKey())
            .appSecret(properties.getAppSecret())
            .baseUrl(properties.getBaseUrl())
            .build();
    }
}
```

## 使用示例

### 1. 基础调用

```java
@Service
@Slf4j
public class ExampleService {
    
    @Autowired
    private DuibaOpenClient client;
    
    public void example() {
        try {
            // 构建请求
            DuibaApiRequest request = DuibaApiRequest.builder()
                .method("example.api.method")
                .params(Collections.singletonMap("key", "value"))
                .build();
                
            // 执行请求
            DuibaApiResponse<ExampleDTO> response = client.execute(request, ExampleDTO.class);
            
            // 处理响应
            if (response.isSuccess()) {
                ExampleDTO data = response.getData();
                // 处理业务逻辑
            } else {
                log.error("API调用失败: code={}, message={}", 
                    response.getCode(), response.getMessage());
            }
        } catch (DuibaApiException e) {
            log.error("API调用异常", e);
        }
    }
}
```

### 2. 自定义签名算法

```java
@Service
public class CustomSignatureService implements SignatureService {
    
    @Override
    public String sign(Map<String, Object> params, String secret) {
        // 实现自定义签名算法
    }
    
    @Override
    public boolean verify(Map<String, Object> params, String secret, String sign) {
        // 实现自定义签名验证
    }
}
```

## 异常处理

SDK定义了以下异常类型：

```java
// API调用异常
public class DuibaApiException extends RuntimeException {
    private String errorCode;
    private String errorMsg;
}

// 参数验证异常
public class DuibaValidationException extends RuntimeException {
    private List<String> validationErrors;
}

// 签名异常
public class DuibaSignatureException extends RuntimeException {
    private String signType;
    private String message;
}
```

## 最佳实践

### 1. 连接池配置

推荐的连接池配置：

```yaml
duiba:
  open:
    sdk:
      http:
        maxConnTotal: 200        # 总连接数
        maxConnPerRoute: 50      # 每个路由的最大连接数
        connectTimeout: 5000     # 连接超时（毫秒）
        readTimeout: 5000        # 读取超时（毫秒）
        connectionRequestTimeout: 1000  # 连接请求超时（毫秒）
```

### 2. 重试策略

```java
@Configuration
public class RetryConfig {
    
    @Bean
    public RetryTemplate retryTemplate() {
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(3);
        
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(1000);
        backOffPolicy.setMultiplier(2);
        
        RetryTemplate template = new RetryTemplate();
        template.setRetryPolicy(retryPolicy);
        template.setBackOffPolicy(backOffPolicy);
        
        return template;
    }
}
```

### 3. 日志配置

```xml
<logger name="com.duiba.sdk" level="INFO">
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="FILE"/>
</logger>
```

## 性能优化

1. **连接池管理**
   - 合理配置连接池大小
   - 及时释放连接
   - 监控连接池状态

2. **请求优化**
   - 使用异步请求
   - 批量请求合并
   - 响应数据压缩

3. **缓存策略**
   - 合理使用本地缓存
   - 实现分布式缓存
   - 缓存预热机制

## 安全建议

1. **密钥管理**
   - 使用配置中心管理密钥
   - 定期轮换密钥
   - 加密存储密钥

2. **请求安全**
   - 使用HTTPS传输
   - 实现请求签名
   - 防重放攻击

3. **数据安全**
   - 敏感数据加密
   - 数据脱敏处理
   - 访问权限控制

## 常见问题

### 1. 签名验证失败
- 检查参数排序是否正确
- 验证密钥是否正确
- 确认签名算法实现

### 2. 连接池异常
- 检查连接池配置
- 监控连接池使用情况
- 分析连接泄漏

### 3. 响应超时
- 调整超时配置
- 检查网络状况
- 开启请求重试

## 版本历史

### 1.0.0-SNAPSHOT
- 初始版本
- 基础功能实现
- 核心组件开发 