plugins {
    id 'java-library'
}

dependencies {
    // Core utilities that the SDK might need
    api 'org.apache.commons:commons-lang3:3.12.0' // Or manage version via root project
    api 'commons-codec:commons-codec:1.15' // Already present, good.

    // For JSON processing
    api 'com.alibaba:fastjson:1.2.83' // Added FastJSON
    // api 'com.fasterxml.jackson.core:jackson-databind:2.13.4' // Commenting out <PERSON> as <PERSON>son<PERSON><PERSON> uses FastJSON

    // SLF4J API for Lombok @Slf4j
    api 'org.slf4j:slf4j-api:1.7.32' // Added SLF4J API

    // HTTP client library
    api 'org.apache.httpcomponents:httpclient:4.5.13' // Added Apache HttpClient

    // Validation annotations for models (DuibaApiRequest, etc.)
    api 'javax.validation:validation-api:2.0.1.Final'

    // Lombok is provided by the root project
    // No Spring Boot starters like -web or -validation as this is a core library
    // No spring-boot-configuration-processor unless it's explicitly for Spring Boot usage of this SDK
}

// This module is a library
/*
bootJar {
    enabled = false
}
*/

jar {
    enabled = true
}

test {
    useJUnitPlatform()
    // You might want to add test logging configuration here if needed
}

// Add test dependencies for WireMock and Logback
dependencies {
    testImplementation 'com.github.tomakehurst:wiremock-jre8:2.35.1'
    testImplementation 'ch.qos.logback:logback-classic:1.2.11' // For test logging
    // JUnit 5 is already in the root project's subprojects.dependencies block for testImplementation
    // testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.2'
    // testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.2'
} 