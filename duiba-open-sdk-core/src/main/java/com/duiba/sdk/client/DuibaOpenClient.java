package com.duiba.sdk.client;

import com.alibaba.fastjson.TypeReference;
import com.duiba.sdk.model.DuibaApiRequest;
import com.duiba.sdk.model.DuibaApiResponse;
import com.duiba.sdk.security.SignatureService;
import com.duiba.sdk.util.JsonUtil;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 兑吧开放平台API客户端。
 * <p>
 * 该客户端封装了调用兑吧开放平台API的通用逻辑，包括参数准备、签名生成、HTTP请求发送及响应解析。
 * <p>
 * <b>线程安全性:</b> 此客户端在正确配置和初始化后是线程安全的。其内部使用的 {@link CloseableHttpClient}
 * 实例是线程安全的，并且客户端本身的状态（如appKey, appSecret, baseUrl）在初始化后不可变。
 * <p>
 * <b>在Spring环境中使用:</b>
 * 建议将 {@code DuibaOpenClient} 配置为一个Spring Bean，以便复用底层的 {@link CloseableHttpClient} 实例，
 * 从而提高性能并避免资源浪费。{@code CloseableHttpClient} 管理着连接池，每次请求都创建新的客户端会导致效率低下。
 * <pre>{@code
 * import com.duiba.sdk.client.DuibaOpenClient;
 * import com.duiba.sdk.security.SignatureService;
 * import com.duiba.sdk.security.impl.MD5SignatureService;
 * import org.springframework.beans.factory.annotation.Value;
 * import org.springframework.context.annotation.Bean;
 * import org.springframework.context.annotation.Configuration;
 *
 * @Configuration
 * public class DuibaOpenClientConfig {
 *
 *     @Value("${duiba.openapi.appKey}")
 *     private String appKey;
 *
 *     @Value("${duiba.openapi.appSecret}")
 *     private String appSecret;
 *
 *     @Value("${duiba.openapi.baseUrl}")
 *     private String baseUrl;
 *
 *     @Bean
 *     public SignatureService md5SignatureService() {
 *         return new MD5SignatureService();
 *     }
 *
 *     @Bean(destroyMethod = "close") // Important: register custom destroy method for HttpClient
 *     public DuibaOpenClient duibaOpenClient(SignatureService signatureService) {
 *         return DuibaOpenClient.builder()
 *                 .appKey(appKey)
 *                 .appSecret(appSecret)
 *                 .baseUrl(baseUrl)
 *                 .signatureService(signatureService) // Or new MD5SignatureService() directly
 *                 .connectTimeout(5000) // 连接超时时间，单位毫秒
 *                 .socketTimeout(10000) // 读取超时时间，单位毫秒
 *                 .build();
 *     }
 * }
 * }</pre>
 * <p>
 * 注意: 当将 {@code DuibaOpenClient} 注册为Spring Bean时，需要确保底层的 {@code CloseableHttpClient} 在应用关闭时被正确关闭。
 * 上述示例中通过 {@code @Bean(destroyMethod = "close")} 来指定 {@code DuibaOpenClient} 的 {@code close} 方法作为销毁方法，
 * 该方法会关闭内部的HttpClient。
 *
 * @see DuibaApiRequest
 * @see DuibaApiResponse
 */
@Slf4j
@Builder
public class DuibaOpenClient {
    
    private final String appKey;
    private final String appSecret;
    private final String baseUrl;
    private final SignatureService signatureService;
    private final CloseableHttpClient httpClient;
    
    public static class DuibaOpenClientBuilder {
        private String appKey;
        private String appSecret;
        private String baseUrl;
        private SignatureService signatureService;
        private CloseableHttpClient httpClient;
        private int connectTimeout = 3000; // ms
        private int socketTimeout = 5000; // ms

        public DuibaOpenClientBuilder appKey(String appKey) { this.appKey = appKey; return this; }
        public DuibaOpenClientBuilder appSecret(String appSecret) { this.appSecret = appSecret; return this; }
        public DuibaOpenClientBuilder baseUrl(String baseUrl) { this.baseUrl = baseUrl; return this; }
        public DuibaOpenClientBuilder signatureService(SignatureService signatureService) { this.signatureService = signatureService; return this; }

        /**
         * 设置连接超时时间。
         * @param connectTimeout 连接超时时间，单位毫秒。
         * @return this builder
         */
        public DuibaOpenClientBuilder connectTimeout(int connectTimeout) {
            this.connectTimeout = connectTimeout;
            return this;
        }

        /**
         * 设置读取超时时间（Socket超时）。
         * @param socketTimeout 读取超时时间，单位毫秒。
         * @return this builder
         */
        public DuibaOpenClientBuilder socketTimeout(int socketTimeout) {
            this.socketTimeout = socketTimeout;
            return this;
        }
        
        public DuibaOpenClient build() {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(this.connectTimeout)
                    .setSocketTimeout(this.socketTimeout)
                    .build();
            if (this.httpClient == null) {
                 this.httpClient = HttpClients.custom()
                    .setDefaultRequestConfig(requestConfig)
                    // Consider adding more robust HttpClient configurations here if needed
                    // e.g., connection pooling, retry handlers, keep-alive strategy
                    .build();
            }
            return new DuibaOpenClient(this.appKey, this.appSecret, this.baseUrl, this.signatureService, this.httpClient);
        }
    }
    
    private DuibaOpenClient(String appKey, String appSecret, String baseUrl, SignatureService signatureService, CloseableHttpClient httpClient) {
        this.appKey = appKey;
        this.appSecret = appSecret;
        this.baseUrl = baseUrl;
        this.signatureService = signatureService;
        this.httpClient = httpClient; // This httpClient is now managed and potentially shared
    }
    
    public <T> DuibaApiResponse<T> execute(DuibaApiRequest request, Class<T> responseDataClass) {
        try {
            // 1. 准备请求参数
            Map<String, Object> requestParams = prepareRequestParams(request);
            
            // 2. 生成签名
            String sign = signatureService.sign(requestParams, appSecret);
            requestParams.put("sign", sign);
            
            // Add uniqueRequestId to request params if present
            if (StringUtils.isNotBlank(request.getUniqueRequestId())) {
                requestParams.put("unique_request_id", request.getUniqueRequestId());
            }
            
            // 3. 发送请求
            HttpPost httpPost = new HttpPost(baseUrl);
            String jsonPayload = JsonUtil.toJson(requestParams);
            StringEntity stringEntity = new StringEntity(jsonPayload, ContentType.APPLICATION_JSON);
            httpPost.setEntity(stringEntity);
            
            log.info("Sending request to {}, uniqueRequestId: {}, payload: {}", baseUrl, request.getUniqueRequestId(), jsonPayload);
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                String responseString = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
                log.info("Received response for uniqueRequestId: {}, response: {}", request.getUniqueRequestId(), responseString);
                EntityUtils.consume(responseEntity); // Ensure response is fully consumed
                
                // 4. 解析响应
                if (responseString == null || responseString.isEmpty()) {
                    return DuibaApiResponse.error("EMPTY_RESPONSE", "Received empty response from server");
                }
                // Construct TypeReference for DuibaApiResponse<T> where T is responseDataClass
                TypeReference<DuibaApiResponse<T>> typeRef = new TypeReference<DuibaApiResponse<T>>(responseDataClass) {};
                return JsonUtil.parseObject(responseString, typeRef);
            }
        } catch (Exception e) {
            log.error("Failed to execute request for uniqueRequestId: {}, params: {}", request.getUniqueRequestId(), JsonUtil.toJson(request), e);
            return DuibaApiResponse.error("SYSTEM_ERROR", e.getMessage());
        }
    }
    
    private Map<String, Object> prepareRequestParams(DuibaApiRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("method", request.getMethod());
        params.put("app_key", appKey);
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        params.put("version", StringUtils.defaultString(request.getVersion(), "1.0"));
        params.put("format", StringUtils.defaultString(request.getFormat(), "json"));
        params.put("charset", StringUtils.defaultString(request.getCharset(), "utf-8"));
        params.put("sign_type", StringUtils.defaultString(request.getSignType(), "md5"));
        
        if (request.getParams() != null) {
            params.putAll(request.getParams());
        }
        
        return params;
    }

    /**
     * 关闭底层的HttpClient连接池。
     * 当此客户端作为Spring Bean并配置了 destroyMethod = "close" 时，此方法会被自动调用。
     * 如果手动管理客户端实例，应在不再需要客户端时调用此方法以释放资源。
     */
    @PreDestroy
    public void close() {
        if (this.httpClient != null) {
            try {
                this.httpClient.close();
                log.info("DuibaOpenClient's HttpClient has been closed.");
            } catch (IOException e) {
                log.error("Error closing DuibaOpenClient's HttpClient", e);
            }
        }
    }
} 