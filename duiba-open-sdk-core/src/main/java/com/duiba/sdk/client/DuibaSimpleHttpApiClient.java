package com.duiba.sdk.client;

import com.duiba.sdk.config.OpenApiConfig;
import com.duiba.sdk.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * OpenAPI客户端
 */
@Slf4j
public class DuibaSimpleHttpApiClient {

    private final OpenApiConfig config;
    
    private final CloseableHttpClient httpClient;

    public DuibaSimpleHttpApiClient(OpenApiConfig config) {
        this.config = config;
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(config.getConnectTimeout() != null ? config.getConnectTimeout() : 3000)
                .setSocketTimeout(config.getReadTimeout() != null ? config.getReadTimeout() : 5000)
                .build();
        this.httpClient = HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .build();
    }

    public DuibaSimpleHttpApiClient(OpenApiConfig config, CloseableHttpClient httpClient) {
        this.config = config;
        this.httpClient = httpClient;
    }
    
    /**
     * 发送GET请求
     *
     * @param path 请求路径
     * @param params 请求参数 (Object for flexibility, often a Map<String, String>)
     * @return 响应结果
     */
    public <T> T doGet(String path, Map<String, String> params, Class<T> responseType) {
        String url = buildUrl(path);
        log.info("GET请求, url: {}, params: {}", url, params);
        
        try {
            URIBuilder uriBuilder = new URIBuilder(url);
            if (params != null) {
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    uriBuilder.addParameter(entry.getKey(), entry.getValue());
                }
            }
            URI uri = uriBuilder.build();
            HttpGet httpGet = new HttpGet(uri);

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                String responseString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                log.info("GET响应, response: {}", responseString);
                return JsonUtil.parseObject(responseString, responseType);
            }
        } catch (Exception e) {
            log.error("GET请求异常", e);
            throw new RuntimeException("GET request failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * 发送POST请求
     *
     * @param path 请求路径
     * @param body 请求体 (Object that can be serialized to JSON)
     * @return 响应结果
     */
    public <T> T doPost(String path, Object body, Class<T> responseType) {
        String url = buildUrl(path);
        log.info("POST请求, url: {}, body: {}", url, JsonUtil.toJson(body));
        
        try {
            HttpPost httpPost = new HttpPost(url);
            String jsonBody = JsonUtil.toJson(body);
            StringEntity stringEntity = new StringEntity(jsonBody, ContentType.APPLICATION_JSON);
            httpPost.setEntity(stringEntity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                log.info("POST响应, response: {}", responseString);
                return JsonUtil.parseObject(responseString, responseType);
            }
        } catch (Exception e) {
            log.error("POST请求异常", e);
            throw new RuntimeException("POST request failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建完整的请求URL
     */
    private String buildUrl(String path) {
        String gateway = config.getGatewayUrl();
        if (gateway.endsWith("/") && path.startsWith("/")) {
            return gateway + path.substring(1);
        } else if (!gateway.endsWith("/") && !path.startsWith("/")) {
            return gateway + "/" + path;
        }
        return gateway + path;
    }
} 