package com.duiba.sdk.model;

import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
@Builder
public class DuibaApiRequest {
    
    @NotBlank(message = "API方法名不能为空")
    private String method;
    
    /**
     * 请求唯一流水号，用于链路追踪，建议调用方生成并传递UUID
     */
    private String uniqueRequestId;
    
    private Map<String, Object> params;
    
    private String version;
    
    private String format;
    
    private String charset;
    
    private String signType;
    
    private String timestamp;
    
    private String appKey;
    
    private String sign;
} 