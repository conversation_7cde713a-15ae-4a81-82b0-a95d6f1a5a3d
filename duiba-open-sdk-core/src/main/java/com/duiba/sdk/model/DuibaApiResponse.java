package com.duiba.sdk.model;

import lombok.Data;

@Data
public class DuibaApiResponse<T> {
    
    private boolean success;
    
    private String code;
    
    private String message;
    
    private T data;
    
    private String requestId;
    
    public static <T> DuibaApiResponse<T> success(T data) {
        DuibaApiResponse<T> response = new DuibaApiResponse<>();
        response.setSuccess(true);
        response.setCode("200");
        response.setMessage("success");
        response.setData(data);
        return response;
    }
    
    public static <T> DuibaApiResponse<T> error(String code, String message) {
        DuibaApiResponse<T> response = new DuibaApiResponse<>();
        response.setSuccess(false);
        response.setCode(code);
        response.setMessage(message);
        return response;
    }
} 