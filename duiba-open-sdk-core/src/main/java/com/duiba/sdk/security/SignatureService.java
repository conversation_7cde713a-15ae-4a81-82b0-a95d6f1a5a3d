package com.duiba.sdk.security;

import java.util.Map;

public interface SignatureService {
    
    /**
     * 生成签名
     *
     * @param params 待签名参数
     * @param secret 密钥
     * @return 签名结果
     */
    String sign(Map<String, Object> params, String secret);
    
    /**
     * 验证签名
     *
     * @param params 待验证参数
     * @param secret 密钥
     * @param sign   签名值
     * @return 验证结果
     */
    boolean verify(Map<String, Object> params, String secret, String sign);
} 