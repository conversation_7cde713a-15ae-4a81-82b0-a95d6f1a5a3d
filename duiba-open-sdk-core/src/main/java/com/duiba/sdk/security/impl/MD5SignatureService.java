package com.duiba.sdk.security.impl;

import com.duiba.sdk.security.SignatureService;
import com.duiba.sdk.util.SignUtil;
// import org.springframework.stereotype.Service;

import java.util.Map;

// @Service("md5SignatureService")
public class MD5SignatureService implements SignatureService {
    
    @Override
    public String sign(Map<String, Object> params, String secret) {
        return SignUtil.md5Sign(params, secret);
    }
    
    @Override
    public boolean verify(Map<String, Object> params, String secret, String sign) {
        String calculatedSign = sign(params, secret);
        return calculatedSign.equals(sign);
    }
} 